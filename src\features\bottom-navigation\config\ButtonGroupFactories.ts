import type { ControlPanelButton } from "@/features/bottom-navigation/control-panel"
import type { 
  BetOptionConfig, 
  SpecialBetConfig, 
  SplitBetConfig, 
  ControlPanelContext 
} from "./ControlPanelTypes"
import { useBettingStore } from "@/stores/betting-store"

// Helper function to create bet option buttons from configuration
export const createBetOptionButton = (
  config: BetOptionConfig,
  context: ControlPanelContext
): ControlPanelButton => {
  const { state, handlers } = context
  const baseButton = {
    id: config.id,
    src: `/assets/images/nav-buttons/${config.icon}`,
    alt: config.alt,
  }

  switch (config.action) {
    case "autoplay":
      return {
        ...baseButton,
        onClick: () => {
          if (state.autoplayActive) {
            handlers.handleBetAction("stop-autoplay")
          } else {
            handlers.handleBetAction("start-autoplay")
          }
        },
        active: state.autoplayActive,
        disabled: !state.Betting,
      }
    case "favourites":
      return {
        ...baseButton,
        onClick: () => handlers.setFavoritesPopoverOpen(true),
        disabled: !state.Betting,
      }
    case "stats":
      return {
        ...baseButton,
        onClick: () => handlers.setBetHistoryPopoverOpen(true),
        disabled: !state.Betting,
      }
    case "hot-cold":
      return {
        ...baseButton,
        onClick: () => handlers.setShowHotCold(!state.showHotCold),
      }
    case "past-numbers":
      return {
        ...baseButton,
        onClick: () => handlers.setShowPastNumbers(!state.showPastNumbers),
      }
    default:
      return {
        ...baseButton,
        onClick: () => handlers.handleBetAction(config.action),
        disabled: !state.Betting,
      }
  }
}

// Helper function to create special bet buttons
export const createSpecialBetButton = (
  config: SpecialBetConfig,
  context: ControlPanelContext
): ControlPanelButton => {
  const { state } = context
  
  return {
    id: config.id,
    src: `/assets/images/nav-buttons/${config.icon}`,
    alt: config.alt,
    onClick: () => {
      if (!state.Betting || !state.selectedChip) return
      const addChip = useBettingStore.getState().addChip
      config.cells.forEach((cell) => {
        addChip(cell, state.selectedChip)
      })
    },
    disabled: !state.Betting,
  }
}

// Helper function to create split bet buttons
export const createSplitBetButton = (
  config: SplitBetConfig,
  context: ControlPanelContext
): ControlPanelButton => {
  const { state, handlers } = context
  
  return {
    id: config.id,
    src: `/assets/images/nav-buttons/${config.icon}`,
    alt: config.alt,
    onClick: () => handlers.handleBetAction(config.action),
    disabled: !state.Betting,
  }
}

// Helper function to create specials toggle button
export const createSpecialsToggleButton = (
  context: ControlPanelContext
): ControlPanelButton => {
  const { state, handlers } = context
  
  return {
    id: "specials",
    src: "/assets/images/nav-buttons/Specials.svg",
    alt: "Special Bets",
    onClick: () => handlers.setGameType(state.gameType === "special" ? "normal" : "special"),
    aspect: "freeform" as const,
    active: state.gameType === "special",
  }
}
