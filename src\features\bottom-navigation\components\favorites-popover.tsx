import { useEffect, useState } from "react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent } from "@/components/ui/popover"
import { SVGIcon, useRoundPhase } from "@/hooks"
import { useBettingStore } from "@/stores/betting-store"
import { SelectableCell } from "@/config/selector-config"
import { withErrorBoundary } from "./with-error-boundary"

interface FavoritesPopoverProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

// Inner component that will be wrapped with error boundary
const FavoritesPopoverInner = ({
  open,
  onOpenChange,
}: FavoritesPopoverProps) => {
  const [favorites, setFavorites] = useState<
    Array<{ id: string; name: string; chips: SelectableCell[] }>
  >([])
  const [editingId, setEditingId] = useState<string | null>(null)
  const [newName, setNewName] = useState("")
  const placedChips = useBettingStore((state) => state.placedChips)
  const addChip = useBettingStore((state) => state.addChip)
  const selectedChip = useBettingStore((state) => state.selectedChip)
  const { Spinning } = useRoundPhase()

  const [notification, setNotification] = useState<{
    message: string
    type: "success" | "error" | "info"
    visible: boolean
  }>({ message: "", type: "info", visible: false })

  useEffect(() => {
    const storedFavorites = localStorage.getItem("favorites")
    if (storedFavorites) {
      setFavorites(JSON.parse(storedFavorites))
    }
  }, [])

  useEffect(() => {
    if (Spinning) {
      onOpenChange(false)
    }
  }, [Spinning, onOpenChange])

  // Auto-hide notification after 3 seconds
  useEffect(() => {
    if (notification.visible) {
      const timer = setTimeout(() => {
        setNotification((prev) => ({ ...prev, visible: false }))
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [notification.visible])

  const showNotification = (
    message: string,
    type: "success" | "error" | "info" = "success"
  ) => {
    setNotification({
      message,
      type,
      visible: true,
    })
  }

  const saveFavorite = () => {
    if (favorites.length >= 5) {
      showNotification("Maximum of 5 favorite bets reached", "error")
      return
    }
    const newFavorite = {
      id: Date.now().toString(),
      name: "New Bet",
      chips: placedChips,
    }
    const newFavorites = [...favorites, newFavorite]
    setFavorites(newFavorites)
    localStorage.setItem("favorites", JSON.stringify(newFavorites))
    setEditingId(newFavorite.id)
    setNewName("New Bet")
    showNotification("Bet saved to favorites")
  }

  const loadFavorite = (chips: SelectableCell[]) => {
    chips.forEach((chip) => addChip(chip, selectedChip))
    showNotification("Favorite bet loaded", "info")
  }

  const deleteFavorite = (id: string) => {
    const deletedFav = favorites.find((fav) => fav.id === id)
    const newFavorites = favorites.filter((fav) => fav.id !== id)
    setFavorites(newFavorites)
    localStorage.setItem("favorites", JSON.stringify(newFavorites))
    showNotification(`"${deletedFav?.name}" removed from favorites`, "info")
  }

  const startEditing = (id: string, name: string) => {
    setEditingId(id)
    setNewName(name)
  }

  const saveName = (id: string) => {
    const newFavorites = favorites.map((fav) =>
      fav.id === id ? { ...fav, name: newName } : fav
    )
    setFavorites(newFavorites)
    localStorage.setItem("favorites", JSON.stringify(newFavorites))
    setEditingId(null)
    showNotification(`Bet renamed to "${newName}"`)
  }

  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      <PopoverContent className='w-80 rounded-lg border border-gray-800 bg-black p-6 shadow-lg'>
        {notification.visible && (
          <Alert
            className={`mb-4 relative ${
              notification.type === "success"
                ? "bg-green-900/50 border-green-600"
                : notification.type === "error"
                ? "bg-red-900/50 border-red-600"
                : "bg-blue-900/50 border-blue-600"
            }`}
          >
            <AlertDescription className='text-sm text-white'>
              {notification.message}
            </AlertDescription>
            <Button
              size='icon'
              className='absolute right-2 top-2 h-5 w-5 bg-transparent hover:bg-gray-800/50'
              onClick={() =>
                setNotification((prev) => ({ ...prev, visible: false }))
              }
            >
              <SVGIcon url='/assets/svgs/x.svg' className='h-3 w-3' />
            </Button>
          </Alert>
        )}

        <div className='space-y-4'>
          <div className='flex items-center justify-between border-b border-gray-800 pb-2'>
            <h3 className='text-xl font-bold text-gray-100'>
              Favorites ({favorites.length}/5)
            </h3>
          </div>
          <div className='space-y-3'>
            {favorites.map((fav) => (
              <div
                key={`${fav.id}-fav-bet`}
                className='flex items-center space-x-2 rounded-lg bg-gray-900 p-2 transition-all duration-200 hover:bg-gray-800'
              >
                {editingId === fav.id ? (
                  <Input
                    value={newName}
                    onChange={(e) => setNewName(e.target.value)}
                    className='grow bg-gray-800 text-white'
                  />
                ) : (
                  <span className='grow text-gray-300'>{fav.name}</span>
                )}
                <div className='flex space-x-1'>
                  {editingId !== fav.id && (
                    <Button
                      onClick={() => loadFavorite(fav.chips)}
                      size='sm'
                      className='bg-gray-700 hover:bg-gray-600'
                    >
                      Load
                    </Button>
                  )}
                  {editingId === fav.id ? (
                    <Button
                      onClick={() => saveName(fav.id)}
                      size='icon'
                      className='aspect-square bg-green-900 hover:bg-green-800'
                    >
                      <SVGIcon
                        url='/assets/svgs/save.svg'
                        className='h-4 w-4'
                      />
                    </Button>
                  ) : (
                    <Button
                      onClick={() => startEditing(fav.id, fav.name)}
                      size='icon'
                      className='aspect-square bg-gray-700 hover:bg-gray-600'
                    >
                      <SVGIcon
                        url='/assets/svgs/pencil.svg'
                        className='h-4 w-4'
                      />
                    </Button>
                  )}
                  <Button
                    onClick={() => deleteFavorite(fav.id)}
                    size='icon'
                    className='aspect-square bg-red-900 hover:bg-red-800'
                    variant='destructive'
                  >
                    <SVGIcon
                      url='/assets/svgs/trash-2.svg'
                      className='h-4 w-4'
                    />
                  </Button>
                </div>
              </div>
            ))}
          </div>
          <Button
            onClick={saveFavorite}
            className='w-full bg-gray-700 hover:bg-gray-600'
            disabled={favorites.length >= 5 || placedChips.length === 0}
          >
            Save Current Bet
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Use the HOC to wrap the component with an error boundary
export const FavoritesPopover = withErrorBoundary(
  FavoritesPopoverInner,
  "FavoritesPopover"
)

export default FavoritesPopover
