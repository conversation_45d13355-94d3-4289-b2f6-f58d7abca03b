import { Voisins, Tiers, Orphalins, Zeros } from "@/config/specials-config"
import type { BetOptionConfig, SpecialBetConfig, SplitBetConfig } from "./ControlPanelTypes"

// Button configuration constants for better maintainability
export const BET_OPTIONS_CONFIG: BetOptionConfig[] = [
  { id: "half", icon: "half button.svg", alt: "Half bet", action: "half" },
  { id: "double", icon: "Doubles.svg", alt: "Double bet", action: "double" },
  { id: "undo", icon: "Undo button.svg", alt: "Undo last bet", action: "undo" },
  { id: "repeat", icon: "Repeat.svg", alt: "Repeat last bet", action: "repeat-last" },
  { id: "autoplay", icon: "AutoPlay.svg", alt: "Autoplay", action: "autoplay" },
  { id: "favourites", icon: "Favourites.svg", alt: "Favorites", action: "favourites" },
  { id: "stats", icon: "Statistics.svg", alt: "Statistics", action: "stats" },
  { id: "hot-cold", icon: "HC.svg", alt: "Hot and Cold Numbers", action: "hot-cold" },
  { id: "past-numbers", icon: "PN.svg", alt: "Past Numbers", action: "past-numbers" },
]

export const SPECIAL_BETS_CONFIG: SpecialBetConfig[] = [
  { id: "big-series", icon: "Big Series.svg", alt: "Big Series", cells: Voisins },
  { id: "small-series", icon: "Small Series.svg", alt: "Small Series", cells: Tiers },
  { id: "orphans", icon: "Orphans.svg", alt: "Orphans", cells: Orphalins },
  { id: "zero-spiel", icon: "Zero Spiel.svg", alt: "Zero Spiel", cells: Zeros },
]

export const SPLIT_BETS_CONFIG: SplitBetConfig[] = [
  { id: "black-split", icon: "Black Split.svg", alt: "Black Split", action: "black-split" },
  { id: "red-split", icon: "Red Split.svg", alt: "Red Split", action: "red-split" },
]
