import { useRef, useState, useEffect, useMemo, useCallback } from "react"
import { useMobile } from "@/hooks/use-mobile"
import { useReducedMotion } from "@/hooks/use-reduced-motion"
import { useResponsiveSize } from "@/hooks/use-responsive-size"
import { useTouchGestures } from "@/hooks/use-touch-gestures"
import { imageCache } from "@/lib/image-cache"
import { logger } from "@/middleware"
import type { ControlPanelButton } from "../control-panel"

export interface UseControlPanelLogicProps {
  buttons: ControlPanelButton[]
  buttonSize?: "sm" | "md" | "lg"
  spacing?: number
  autoResponsive?: boolean
  orientation?: "horizontal" | "vertical"
  zeroGapForChips?: boolean
}

export interface ControlPanelLogicState {
  // Refs
  scrollRef: React.RefObject<HTMLDivElement | null>
  scrollAreaRef: React.RefObject<HTMLDivElement | null>

  // State
  isVisible: boolean
  loadedButtons: Record<string, boolean>
  isDragging: boolean
  dragStart: { x: number; y: number }
  scrollStart: { x: number; y: number }
  forceShowButtons: boolean

  // Computed values
  buttonSize: "sm" | "md" | "lg"
  finalSpacing: number
  allImagesLoaded: boolean
  allButtonsAreChips: boolean
  prefersReducedMotion: boolean
  animationConfig: { stiffness: number; damping: number }
  isMobile: boolean

  // Handlers
  checkScrollPosition: () => void
  scrollContent: (amount: number) => void
  handleMouseDown: (e: React.MouseEvent) => void
  setRefs: (element: HTMLDivElement | null) => void
  touchRef: React.RefObject<HTMLDivElement | null>
}

export function useControlPanelLogic({
  buttons,
  buttonSize: propButtonSize,
  spacing = 14,
  autoResponsive = true,
  orientation = "horizontal",
  zeroGapForChips = false,
}: UseControlPanelLogicProps): ControlPanelLogicState {
  const scrollRef = useRef<HTMLDivElement>(null)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [loadedButtons, setLoadedButtons] = useState<Record<string, boolean>>(
    {}
  )

  // Drag-to-scroll state
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [scrollStart, setScrollStart] = useState({ x: 0, y: 0 })
  const [forceShowButtons, setForceShowButtons] = useState(false)
  const loadTimeoutRef = useRef<number | null>(null)

  const checkScrollPosition = useCallback(() => {
    if (scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      )
      if (!viewport) return

      const viewportElement = viewport as HTMLElement

      if (orientation === "vertical") {
        const { scrollTop, scrollHeight, clientHeight } = viewportElement

        logger.debug("Vertical scroll position check", {
          context: "ControlPanel",
          data: {
            scrollTop,
            scrollHeight,
            clientHeight,
            canScrollTop: scrollTop > 0,
            canScrollBottom: scrollTop + clientHeight < scrollHeight,
          },
        })

        // Update scroll indicators for vertical orientation
        const container = scrollRef.current?.parentElement
        if (container) {
          container.classList.toggle("can-scroll-top", scrollTop > 0)
          container.classList.toggle(
            "can-scroll-bottom",
            scrollTop + clientHeight < scrollHeight
          )
        }
      } else {
        const { scrollLeft, scrollWidth, clientWidth } = viewportElement

        logger.debug("Horizontal scroll position check", {
          context: "ControlPanel",
          data: {
            scrollLeft,
            scrollWidth,
            clientWidth,
            canScroll: scrollWidth > clientWidth,
            canScrollLeft: scrollLeft > 0,
            canScrollRight: scrollLeft + clientWidth < scrollWidth,
          },
        })

        // Update scroll indicators for horizontal orientation
        const container = scrollRef.current?.parentElement
        if (container) {
          container.classList.toggle("can-scroll-left", scrollLeft > 0)
          container.classList.toggle(
            "can-scroll-right",
            scrollLeft + clientWidth < scrollWidth
          )
        }
      }
    }
  }, [orientation])

  const responsiveSize = useResponsiveSize(propButtonSize || "lg")
  const buttonSize = autoResponsive ? responsiveSize : propButtonSize || "lg"

  const prefersReducedMotion = useReducedMotion()

  const { ref: touchRef } = useTouchGestures<HTMLDivElement>({
    onSwipeLeft: () =>
      orientation === "horizontal" ? scrollContent(300) : undefined,
    onSwipeRight: () =>
      orientation === "horizontal" ? scrollContent(-300) : undefined,
    onSwipeUp: () =>
      orientation === "vertical" ? scrollContent(300) : undefined,
    onSwipeDown: () =>
      orientation === "vertical" ? scrollContent(-300) : undefined,
  })

  const setRefs = (element: HTMLDivElement | null) => {
    scrollRef.current = element
    if (touchRef.current !== null) {
      touchRef.current = element
    }
  }

  const scrollContent = useCallback(
    (amount: number) => {
      if (scrollAreaRef.current) {
        const viewport = scrollAreaRef.current.querySelector(
          "[data-radix-scroll-area-viewport]"
        )
        if (!viewport) return

        const viewportElement = viewport as HTMLElement

        if (orientation === "vertical") {
          viewportElement.scrollBy({
            top: amount,
            behavior: prefersReducedMotion ? "auto" : "smooth",
          })
        } else {
          viewportElement.scrollBy({
            left: amount,
            behavior: prefersReducedMotion ? "auto" : "smooth",
          })
        }

        setTimeout(checkScrollPosition, 300)
      }
    },
    [prefersReducedMotion, checkScrollPosition, orientation]
  )

  // Drag-to-scroll handlers
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button !== 0) return // Only handle left mouse button

    const viewport = scrollAreaRef.current?.querySelector(
      "[data-radix-scroll-area-viewport]"
    ) as HTMLElement
    if (!viewport) return

    setIsDragging(true)
    setDragStart({ x: e.clientX, y: e.clientY })
    setScrollStart({ x: viewport.scrollLeft, y: viewport.scrollTop })

    // Prevent text selection during drag
    e.preventDefault()
  }, [])

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isDragging) return

      const viewport = scrollAreaRef.current?.querySelector(
        "[data-radix-scroll-area-viewport]"
      ) as HTMLElement
      if (!viewport) return

      const deltaX = e.clientX - dragStart.x
      const deltaY = e.clientY - dragStart.y

      if (orientation === "horizontal") {
        viewport.scrollLeft = scrollStart.x - deltaX
      } else {
        viewport.scrollTop = scrollStart.y - deltaY
      }

      checkScrollPosition()
    },
    [isDragging, dragStart, scrollStart, orientation, checkScrollPosition]
  )

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Add global mouse event listeners for drag-to-scroll
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
      document.addEventListener("mouseleave", handleMouseUp)

      return () => {
        document.removeEventListener("mousemove", handleMouseMove)
        document.removeEventListener("mouseup", handleMouseUp)
        document.removeEventListener("mouseleave", handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  // Image loading logic
  useEffect(() => {
    const initialLoadState: Record<string, boolean> = {}
    const imagesToPreload: string[] = []

    buttons.forEach((button) => {
      if (button && button.id) {
        // Check if image is already cached
        const isLoaded = imageCache.isLoaded(button.src)
        initialLoadState[button.id] = isLoaded

        if (!isLoaded) {
          imagesToPreload.push(button.src)
        }

        logger.debug(`Initial button load state: ${button.id} = ${isLoaded}`, {
          context: "ControlPanel",
          data: { src: button.src },
        })
      }
    })

    setLoadedButtons(initialLoadState)

    // Preload images using cache to prevent redundant requests
    if (imagesToPreload.length > 0) {
      imageCache
        .preloadMultiple(imagesToPreload)
        .then(() => {
          // Update loaded state for all buttons
          const updatedLoadState: Record<string, boolean> = {}
          buttons.forEach((button) => {
            if (button && button.id) {
              updatedLoadState[button.id] = true
            }
          })
          setLoadedButtons(updatedLoadState)
        })
        .catch((error: any) => {
          logger.warn("Failed to preload some button images", {
            context: "ControlPanel",
            data: { error: error.message },
          })
          // Still mark as loaded to prevent infinite loading
          setForceShowButtons(true)
        })
    }

    setIsVisible(true)
    checkScrollPosition()

    if (loadTimeoutRef.current) {
      window.clearTimeout(loadTimeoutRef.current)
    }

    loadTimeoutRef.current = window.setTimeout(() => {
      logger.warn("Forcing button display after timeout", {
        context: "ControlPanel",
      })
      setForceShowButtons(true)
      loadTimeoutRef.current = null
    }, 5000) as unknown as number

    return () => {
      if (loadTimeoutRef.current) {
        window.clearTimeout(loadTimeoutRef.current)
        loadTimeoutRef.current = null
      }
    }
  }, [checkScrollPosition, buttons])

  // Scroll event listeners
  useEffect(() => {
    const scrollArea = scrollAreaRef.current
    if (!scrollArea) return

    const viewport = scrollArea.querySelector(
      "[data-radix-scroll-area-viewport]"
    )
    if (!viewport) return

    const debouncedCheckScroll = () => {
      if (scrollAreaRef.current) {
        requestAnimationFrame(checkScrollPosition)
      }
    }

    const handleWheel = (e: Event) => {
      const wheelEvent = e as WheelEvent

      // Enhanced wheel scrolling for both orientations
      if (orientation === "horizontal") {
        // For horizontal orientation, convert vertical wheel to horizontal scroll
        if (
          wheelEvent.shiftKey ||
          Math.abs(wheelEvent.deltaX) > Math.abs(wheelEvent.deltaY)
        )
          return

        if (Math.abs(wheelEvent.deltaY) > 0) {
          e.preventDefault()
          scrollContent(wheelEvent.deltaY * 2)
          debouncedCheckScroll()
        }
      } else {
        // For vertical orientation, enhance vertical scrolling
        if (Math.abs(wheelEvent.deltaY) > 0) {
          e.preventDefault()
          scrollContent(wheelEvent.deltaY * 1.5)
          debouncedCheckScroll()
        }
      }
    }

    viewport.addEventListener("scroll", debouncedCheckScroll, { passive: true })

    // Add wheel event handler for both orientations
    viewport.addEventListener("wheel", handleWheel, { passive: false })

    window.addEventListener("resize", debouncedCheckScroll)

    checkScrollPosition()

    return () => {
      viewport.removeEventListener("scroll", debouncedCheckScroll)
      viewport.removeEventListener("wheel", handleWheel)
      window.removeEventListener("resize", debouncedCheckScroll)
    }
  }, [checkScrollPosition, scrollContent, orientation])

  // Continue loading images as buttons change
  useEffect(() => {
    const newLoadState = { ...loadedButtons }
    let hasChanges = false
    const imagesToPreload: string[] = []

    buttons.forEach((button) => {
      if (button && button.id && !loadedButtons[button.id]) {
        // Check if image is already cached
        if (imageCache.isLoaded(button.src)) {
          newLoadState[button.id] = true
          hasChanges = true
          logger.debug(`Button image already in cache: ${button.id}`, {
            context: "ControlPanel",
          })
        } else {
          imagesToPreload.push(button.src)
        }
      }
    })

    // Preload any missing images using cache
    if (imagesToPreload.length > 0) {
      imagesToPreload.forEach((src) => {
        imageCache
          .preload(src)
          .then(() => {
            const button = buttons.find((b) => b.src === src)
            if (button) {
              logger.debug(`Button image loaded: ${button.id}`, {
                context: "ControlPanel",
              })
              setLoadedButtons((prev: any) => ({
                ...prev,
                [button.id]: true,
              }))
            }
          })
          .catch(() => {
            const button = buttons.find((b) => b.src === src)
            if (button) {
              logger.warn(`Button image load error: ${button.id}`, {
                context: "ControlPanel",
                data: { src: button.src },
              })
              setLoadedButtons((prev: any) => ({
                ...prev,
                [button.id]: true,
              }))
            }
          })
      })
    }

    if (hasChanges) {
      setLoadedButtons(newLoadState)
    }
  }, [buttons, loadedButtons])

  const allImagesLoaded = useMemo(
    () =>
      buttons.every(
        (button) => button && button.id && loadedButtons[button.id]
      ),
    [buttons, loadedButtons]
  )

  const allButtonsAreChips = useMemo(
    () =>
      buttons.length > 0 && buttons.every((button) => button && button.isChip),
    [buttons]
  )

  const finalSpacing = useMemo(
    () => (allButtonsAreChips && zeroGapForChips ? 0 : spacing),
    [allButtonsAreChips, zeroGapForChips, spacing]
  )

  const animationConfig = prefersReducedMotion
    ? { stiffness: 200, damping: 30 }
    : { stiffness: 100, damping: 20 }

  const isMobile = useMobile()

  return {
    scrollRef,
    scrollAreaRef,
    isVisible,
    loadedButtons,
    isDragging,
    dragStart,
    scrollStart,
    forceShowButtons,
    buttonSize,
    finalSpacing,
    allImagesLoaded,
    allButtonsAreChips,
    prefersReducedMotion,
    animationConfig,
    isMobile,
    checkScrollPosition,
    scrollContent,
    handleMouseDown,
    setRefs,
    touchRef,
  }
}
