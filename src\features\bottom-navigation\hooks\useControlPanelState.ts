import { useRoundPhase } from "@/hooks"
import { useBettingStore } from "@/stores/betting-store"
import { useGameStateStore } from "@/stores/game-state-store"
import type { ControlPanelState } from "../config/ControlPanelTypes"

/**
 * Hook to manage control panel state
 * Centralizes all state selectors for control panels
 */
export const useControlPanelState = (): ControlPanelState => {
  const { Betting } = useRoundPhase()
  const selectedChip = useBettingStore((state) => state.selectedChip)
  const autoplayActive = useBettingStore((state) => state.autoplay.active)
  
  const {
    gameType,
    showPastNumbers,
    showHotCold,
  } = useGameStateStore()

  return {
    Betting,
    autoplayActive,
    showHotCold,
    showPastNumbers,
    gameType,
    selectedChip,
  }
}
