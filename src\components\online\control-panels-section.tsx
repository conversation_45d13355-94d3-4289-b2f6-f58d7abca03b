import GameSection from "@/components/ui/game-section"
import { ControlPanel } from "@/features/bottom-navigation"
import FavoritesPopover from "@/features/bottom-navigation/components/favorites-popover"
import { BetHistoryPopover } from "@/features/bottom-navigation/components/bet-history-popover"
import { useControlPanels } from "@/hooks/game/use-control-panels"
import { cn } from "@/lib/utils"
import { LAYOUT_CONSTANTS, PlayerFinancialData } from "./layout-constants"
import PlayerBalanceDisplay from "./player-balance-display"

interface ControlPanelsSectionProps extends PlayerFinancialData {
  displayMode: "mobile" | "desktop"
}

interface ControlPanelConfiguration {
  showTooltips: boolean
  autoSwitch: boolean
  useDeviceOrientation: boolean
}

const useStandardControlPanelConfiguration = (): ControlPanelConfiguration => ({
  showTooltips: true,
  autoSwitch: true,
  useDeviceOrientation: true,
})

const ControlPanelsGrid = () => {
  const {
    betOptionsButtons,
    chipsButtons,
    controlsButtons,
    betHistoryPopoverOpen,
    setBetHistoryPopoverOpen,
  } = useControlPanels()
  const standardControlPanelConfiguration =
    useStandardControlPanelConfiguration()

  return (
    <>
      <div
        className={cn(
          `w-full grid grid-cols-[repeat(${LAYOUT_CONSTANTS.CONTROL_PANEL_GRID_COLUMNS},1fr)] grid-rows-1 gap-4 min-h-0`
        )}
      >
        <GameSection
          hasInsetShadow={false}
          className={`w-full h-auto md:row-span-1 col-span-${LAYOUT_CONSTANTS.CONTROL_PANEL_SPANS.BET_OPTIONS}`}
          title='Bet Options'
        >
          <ControlPanel
            buttons={betOptionsButtons}
            {...standardControlPanelConfiguration}
          />
        </GameSection>

        <GameSection
          hasInsetShadow={false}
          className={`w-full h-auto md:row-span-1 col-span-${LAYOUT_CONSTANTS.CONTROL_PANEL_SPANS.CHIPS}`}
          title='Chips'
        >
          <ControlPanel
            buttons={chipsButtons}
            zeroGapForChips={true}
            {...standardControlPanelConfiguration}
          />
        </GameSection>

        <GameSection
          hasInsetShadow={false}
          className={`w-full h-auto md:row-span-1 col-span-${LAYOUT_CONSTANTS.CONTROL_PANEL_SPANS.CONTROLS}`}
          title='Controls'
        >
          <ControlPanel
            buttons={controlsButtons}
            {...standardControlPanelConfiguration}
          />
        </GameSection>
      </div>

      {/* Popovers positioned outside the grid to avoid layout issues */}
      <FavoritesPopover />
      <BetHistoryPopover />
    </>
  )
}

const ControlPanelsSection = ({
  displayMode,
  currentBalance,
  totalBetAmount,
  lastWinAmount,
}: ControlPanelsSectionProps) => (
  <section className='col-span-full row-start-3 min-h-0 mx-4 md:mx-0 max-h-[20vh] z-30'>
    <ControlPanelsGrid />

    <PlayerBalanceDisplay
      displayMode={displayMode}
      currentBalance={currentBalance}
      totalBetAmount={totalBetAmount}
      lastWinAmount={lastWinAmount}
    />
  </section>
)

export default ControlPanelsSection
