import { ControlButton } from "./control-button"
import { BetH<PERSON>oryPopover } from "./bet-history-popover"
import type { ControlPanelButton } from "../control-panel"

interface BetHistoryButtonWithPopoverProps {
  button: ControlPanelButton
  open: boolean
  onOpenChange: (open: boolean) => void
  buttonSize: "sm" | "md" | "lg"
  showTooltip?: boolean
  activeColor: string
  hoverColor: string
}

export function BetHistoryButtonWithPopover({
  button,
  open,
  onOpenChange,
  buttonSize,
  showTooltip,
  activeColor,
  hoverColor,
}: BetHistoryButtonWithPopoverProps) {
  return (
    <div className="relative">
      <ControlButton
        id={button.id}
        src={button.src}
        alt={button.alt}
        onClick={() => onOpenChange(true)}
        disabled={button.disabled}
        active={button.active}
        size={buttonSize}
        showTooltip={showTooltip}
        activeColor={activeColor}
        hoverColor={hoverColor}
        value={button.value}
        isChip={button.isChip}
        aspect={button.aspect}
      />
      <BetHistoryPopover open={open} onOpenChange={onOpenChange} />
    </div>
  )
}
