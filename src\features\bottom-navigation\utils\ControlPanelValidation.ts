import type { ControlPanelButton } from "@/features/bottom-navigation/control-panel"
import type { 
  BetOptionConfig, 
  SpecialBetConfig, 
  SplitBetConfig,
  ControlPanelState,
  ControlPanelHandlers 
} from "../config/ControlPanelTypes"

/**
 * Validation utilities for control panel configurations
 */

// Validate control panel state
export const validateControlPanelState = (state: ControlPanelState): boolean => {
  return (
    typeof state.Betting === 'boolean' &&
    typeof state.autoplayActive === 'boolean' &&
    typeof state.showHotCold === 'boolean' &&
    typeof state.showPastNumbers === 'boolean' &&
    typeof state.gameType === 'string' &&
    state.gameType.length > 0
  )
}

// Validate control panel handlers
export const validateControlPanelHandlers = (handlers: ControlPanelHandlers): boolean => {
  return (
    typeof handlers.handleBetAction === 'function' &&
    typeof handlers.setFavoritesPopoverOpen === 'function' &&
    typeof handlers.setBetHistoryPopoverOpen === 'function' &&
    typeof handlers.setShowHotCold === 'function' &&
    typeof handlers.setShowPastNumbers === 'function' &&
    typeof handlers.setGameType === 'function'
  )
}

// Validate button configuration array
export const validateButtonConfigs = (
  configs: (BetOptionConfig | SpecialBetConfig | SplitBetConfig)[]
): boolean => {
  if (!Array.isArray(configs) || configs.length === 0) {
    return false
  }
  
  return configs.every(config => 
    config.id && 
    config.icon && 
    config.alt &&
    config.id.length > 0 &&
    config.icon.length > 0 &&
    config.alt.length > 0
  )
}

// Validate generated button
export const validateGeneratedButton = (button: ControlPanelButton): boolean => {
  return (
    typeof button.id === 'string' &&
    typeof button.src === 'string' &&
    typeof button.alt === 'string' &&
    button.id.length > 0 &&
    button.src.length > 0 &&
    button.alt.length > 0 &&
    (button.onClick === undefined || typeof button.onClick === 'function') &&
    (button.disabled === undefined || typeof button.disabled === 'boolean') &&
    (button.active === undefined || typeof button.active === 'boolean')
  )
}

// Check for duplicate button IDs
export const checkForDuplicateIds = (buttons: ControlPanelButton[]): string[] => {
  const ids = buttons.map(button => button.id)
  const duplicates: string[] = []
  
  ids.forEach((id, index) => {
    if (ids.indexOf(id) !== index && !duplicates.includes(id)) {
      duplicates.push(id)
    }
  })
  
  return duplicates
}
