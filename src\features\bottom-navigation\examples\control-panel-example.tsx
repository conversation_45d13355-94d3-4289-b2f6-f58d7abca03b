/**
 * Example demonstrating the abstracted Control Panel architecture
 * 
 * This example shows how the control panel now works as an orchestrator
 * between logic (useControlPanelLogic) and presentation (ControlPanelRenderer).
 * 
 * The control panel no longer depends on theme context and uses default theme directly.
 */

import { useState } from "react"
import { ControlPanel, type ControlPanelButton } from "../control-panel"
import { useControlPanelLogic } from "../hooks/useControlPanelLogic"
import { ControlPanelRenderer } from "../components/control-panel-renderer"
import { themeStyles } from "../components/theme-context"

// Example buttons for demonstration
const exampleButtons: ControlPanelButton[] = [
  {
    id: "button1",
    src: "/images/example-button-1.png",
    alt: "Example Button 1",
    onClick: () => console.log("Button 1 clicked"),
  },
  {
    id: "button2",
    src: "/images/example-button-2.png",
    alt: "Example Button 2",
    onClick: () => console.log("Button 2 clicked"),
    active: true,
  },
  {
    id: "button3",
    src: "/images/example-button-3.png",
    alt: "Example Button 3",
    onClick: () => console.log("Button 3 clicked"),
    disabled: true,
  },
]

/**
 * Example 1: Using the Control Panel as an orchestrator (recommended)
 * 
 * This is the standard way to use the control panel. It automatically
 * coordinates between logic and presentation layers.
 */
export function ControlPanelOrchestratorExample() {
  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Control Panel as Orchestrator</h2>
      <p className="text-sm text-gray-600">
        The control panel acts as an orchestrator, coordinating between logic and presentation.
        No theme context dependency - uses default theme directly.
      </p>
      
      <ControlPanel
        buttons={exampleButtons}
        showTooltips={true}
        orientation="horizontal"
        theme="default" // Uses default theme directly, no context needed
      />
    </div>
  )
}

/**
 * Example 2: Using the logic hook directly with custom renderer
 * 
 * This shows how you can use the logic hook directly if you need
 * custom rendering behavior while still getting all the functionality.
 */
export function CustomRendererExample() {
  const logic = useControlPanelLogic({
    buttons: exampleButtons,
    buttonSize: "md",
    spacing: 16,
    orientation: "horizontal",
  })

  const defaultTheme = themeStyles.default

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Custom Renderer with Logic Hook</h2>
      <p className="text-sm text-gray-600">
        Using the logic hook directly with the presentation component for custom behavior.
      </p>
      
      <ControlPanelRenderer
        buttons={exampleButtons}
        borderColor={defaultTheme.borderColor}
        backgroundColor={defaultTheme.backgroundColor}
        activeColor={defaultTheme.activeColor}
        hoverColor={defaultTheme.hoverColor}
        showTooltips={true}
        orientation="horizontal"
        logic={logic}
      />
    </div>
  )
}

/**
 * Example 3: Different themes without context
 * 
 * Shows how themes work without requiring context providers.
 */
export function ThemeExample() {
  const [selectedTheme, setSelectedTheme] = useState<"default" | "dark" | "light" | "colorful">("default")

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Theme Selection (No Context Required)</h2>
      <p className="text-sm text-gray-600">
        Themes work directly without requiring context providers.
      </p>
      
      <div className="flex gap-2 mb-4">
        {(["default", "dark", "light", "colorful"] as const).map((theme) => (
          <button
            key={theme}
            onClick={() => setSelectedTheme(theme)}
            className={`px-3 py-1 rounded text-sm ${
              selectedTheme === theme 
                ? "bg-blue-500 text-white" 
                : "bg-gray-200 text-gray-700"
            }`}
          >
            {theme}
          </button>
        ))}
      </div>
      
      <ControlPanel
        buttons={exampleButtons}
        theme={selectedTheme}
        showTooltips={true}
        orientation="horizontal"
      />
    </div>
  )
}

/**
 * Example 4: Responsive orientation
 * 
 * Shows how the control panel handles different orientations.
 */
export function ResponsiveExample() {
  const [orientation, setOrientation] = useState<"horizontal" | "vertical">("horizontal")

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Responsive Orientation</h2>
      <p className="text-sm text-gray-600">
        Control panel supports both horizontal and vertical orientations.
      </p>
      
      <div className="flex gap-2 mb-4">
        <button
          onClick={() => setOrientation("horizontal")}
          className={`px-3 py-1 rounded text-sm ${
            orientation === "horizontal" 
              ? "bg-blue-500 text-white" 
              : "bg-gray-200 text-gray-700"
          }`}
        >
          Horizontal
        </button>
        <button
          onClick={() => setOrientation("vertical")}
          className={`px-3 py-1 rounded text-sm ${
            orientation === "vertical" 
              ? "bg-blue-500 text-white" 
              : "bg-gray-200 text-gray-700"
          }`}
        >
          Vertical
        </button>
      </div>
      
      <div className={orientation === "vertical" ? "h-64" : ""}>
        <ControlPanel
          buttons={exampleButtons}
          orientation={orientation}
          showTooltips={true}
        />
      </div>
    </div>
  )
}

/**
 * Complete example page showing all examples
 */
export function ControlPanelExamples() {
  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Control Panel Architecture Examples</h1>
        <p className="text-gray-600">
          Demonstrating the abstracted control panel with separated concerns
        </p>
      </div>
      
      <div className="grid gap-8">
        <ControlPanelOrchestratorExample />
        <CustomRendererExample />
        <ThemeExample />
        <ResponsiveExample />
      </div>
      
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-bold mb-2">Architecture Benefits:</h3>
        <ul className="text-sm space-y-1 text-gray-700">
          <li>• <strong>Separation of Concerns:</strong> Logic and presentation are cleanly separated</li>
          <li>• <strong>No Theme Context Dependency:</strong> Uses default theme directly</li>
          <li>• <strong>Reusable Logic:</strong> useControlPanelLogic can be used with custom renderers</li>
          <li>• <strong>Pure Presentation:</strong> ControlPanelRenderer is a pure component</li>
          <li>• <strong>Orchestrator Pattern:</strong> ControlPanel coordinates between layers</li>
          <li>• <strong>Backward Compatible:</strong> Existing usage continues to work</li>
        </ul>
      </div>
    </div>
  )
}
