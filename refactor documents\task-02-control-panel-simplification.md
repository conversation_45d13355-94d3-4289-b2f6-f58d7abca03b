# Task 02: Control Panel Enhancement & Simplification

## Task Overview

**Priority**: High | **Risk**: Medium | **Impact**: High
**Estimated Duration**: 1 week | **Dependencies**: None

### Description
Enhance the control panels section with dual scrolling functionality (mouse wheel + drag-to-scroll), connect the favorites button to the popover, add scroll indicators, and simplify the implementation following the refactoring guide. Focus on mobile-first touch interactions while maintaining existing functionality.

### Expected Impact
- Add smooth dual scrolling functionality for better UX
- Connect favorites button to popover for complete feature integration
- Implement visual scroll indicators for better discoverability
- Reduce `useControlPanels` hook complexity by 40%
- Improve mobile touch interaction experience
- Maintain all existing control panel functionality

## Current State Analysis

### Control Panels Section Structure
**File**: `src/components/online/control-panels-section.tsx` (97 lines)
- Uses 24-column grid layout with spans: Bet Options (9), Chips (6), Controls (9)
- Contains `FavoritesPopover` component but not connected to favorites button
- Uses `useStandardControlPanelConfiguration` for consistent theming
- Wrapped in `GameSection` components with titles

### Control Panel Component Features
**File**: `src/features/bottom-navigation/control-panel.tsx` (579 lines)
- Already has `ScrollArea` with horizontal/vertical scrolling
- Includes `useTouchGestures` for swipe navigation (lines 143-152)
- Has progressive loading and image caching
- Contains scroll position checking and smooth scrolling (lines 161-186)
- Missing: Mouse wheel scrolling and drag-to-scroll functionality

### useControlPanels Hook Issues
**File**: `src/hooks/game/use-control-panels.tsx` (218 lines)
- **Lines 29-107**: Repetitive bet options configuration (9 buttons)
- **Lines 110-125**: Chips configuration using map function
- **Lines 139-210**: Controls configuration with mixed concerns
- **Line 77**: Favorites button calls `setFavoritesPopoverOpen(true)` but popover not positioned

### Missing Features Identified
1. **Mouse wheel scrolling**: Not implemented in ControlPanel component
2. **Drag-to-scroll**: Touch gestures exist but no mouse drag functionality
3. **Scroll indicators**: No visual cues for scrollable content
4. **Favorites connection**: Button exists but popover positioning needs work

### Existing Strengths
- Touch gestures already implemented via `useTouchGestures`
- ScrollArea component with proper styling
- Image caching and progressive loading
- Responsive design with mobile-first approach
- Error boundaries and accessibility features

## Target Architecture

### Enhanced Control Panel Features
1. **Dual Scrolling Functionality**
   - Mouse wheel scrolling for desktop users
   - Drag-to-scroll with mouse/touch for intuitive navigation
   - Smooth scrolling animations with reduced motion support

2. **Favorites Integration**
   - Connect favorites button to `FavoritesPopover` component
   - Proper positioning and state management
   - Mobile-first interaction design

3. **Visual Scroll Indicators**
   - Fade effects at scroll boundaries
   - Touch-friendly scroll indicators
   - Responsive design for different screen sizes

4. **Simplified Hook Structure**
   - Extract button configuration into constants
   - Reduce repetitive code patterns
   - Maintain existing functionality while improving maintainability

### Implementation Strategy
**Phase 1**: Add scrolling enhancements to existing `ControlPanel` component
**Phase 2**: Connect favorites button to popover with proper positioning
**Phase 3**: Add visual scroll indicators and mobile optimizations
**Phase 4**: Simplify `useControlPanels` hook by extracting configurations

### Target File Structure
```
src/features/bottom-navigation/
├── control-panel.tsx (simplified component)
├── config/
│   ├── ControlPanelConfig.ts (declarative configuration)
│   ├── ButtonGroupFactories.ts (button creation utilities)
│   ├── ButtonStateManagers.ts (state management logic)
│   └── ControlPanelTypes.ts (type definitions)
├── hooks/
│   ├── useControlPanels.ts (simplified hook <50 lines)
│   ├── useButtonGroups.ts (button group management)
│   └── useControlPanelState.ts (state management)
└── utils/
    ├── ButtonConfigUtils.ts (configuration utilities)
    └── ControlPanelValidation.ts (validation logic)
```

### Key Files to Modify
- `src/features/bottom-navigation/control-panel.tsx` - Add mouse wheel & drag scrolling
- `src/components/online/control-panels-section.tsx` - Connect favorites popover
- `src/hooks/game/use-control-panels.tsx` - Simplify and extract configurations
- `src/features/bottom-navigation/control-panel.css` - Add scroll indicator styles

## Step-by-Step Implementation Plan

### Phase 1: Scrolling Enhancements (Days 1-2)

#### Day 1: Add Mouse Wheel & Drag Scrolling
- [ ] Add mouse wheel event handlers to `ControlPanel` component
- [ ] Implement drag-to-scroll functionality using mouse events
- [ ] Ensure smooth scrolling with reduced motion support
- [ ] Test scrolling on desktop and mobile devices

#### Day 2: Enhance Touch Gestures
- [ ] Improve existing touch gesture implementation
- [ ] Add momentum scrolling for better mobile experience
- [ ] Optimize scroll performance and responsiveness
- [ ] Add scroll boundary detection

### Phase 2: Favorites Integration (Day 3)

#### Day 3: Connect Favorites Button to Popover
- [ ] Modify `ControlPanelsGrid` to properly position `FavoritesPopover`
- [ ] Ensure favorites button triggers popover opening
- [ ] Test popover positioning on different screen sizes
- [ ] Verify mobile-first interaction design

### Phase 3: Visual Scroll Indicators (Day 4)

#### Day 4: Add Scroll Indicators
- [ ] Create fade effects at scroll boundaries in CSS
- [ ] Add visual cues for scrollable content
- [ ] Implement responsive scroll indicators
- [ ] Test indicators on various devices and orientations

### Phase 4: Hook Simplification (Days 5-6)

#### Day 5: Extract Button Configurations
- [ ] Create button configuration constants
- [ ] Extract repetitive button definitions
- [ ] Simplify bet options configuration
- [ ] Reduce controls configuration complexity

#### Day 6: Optimize Hook Structure
- [ ] Reduce `useControlPanels` hook line count by 40%
- [ ] Improve memoization and dependency management
- [ ] Maintain all existing functionality
- [ ] Add comprehensive testing

### Phase 5: Testing & Polish (Day 7)

#### Day 7: Integration Testing & Documentation
- [ ] Test all scrolling functionality across devices
- [ ] Verify favorites integration works correctly
- [ ] Validate scroll indicators display properly
- [ ] Ensure no regression in existing features
- [ ] Update component documentation

## Code Examples

### Before: Current useControlPanels Hook
```typescript
export const useControlPanels = () => {
  // 50+ lines of state selectors and dependencies
  
  const betOptionsButtons = useMemo<ControlPanelButton[]>(
    () => [
      {
        id: "repeat",
        src: "/assets/images/nav-buttons/Repeat.svg",
        alt: "Repeat Last Bet",
        onClick: () => handleBetAction("repeat"),
        disabled: !Betting || autoplayActive,
      },
      {
        id: "undo",
        src: "/assets/images/nav-buttons/Undo.svg",
        alt: "Undo Last Bet",
        onClick: () => handleBetAction("undo"),
        disabled: !Betting || autoplayActive,
      },
      // ... 6 more repetitive configurations
    ],
    [handleBetAction, Betting, autoplayActive, showHotCold, showPastNumbers]
  )
  
  // 100+ more lines of similar repetitive code
  
  return { betOptionsButtons, chipsButtons, controlsButtons }
}
```

### After: Simplified Hook with Configuration
```typescript
export const useControlPanels = () => {
  const context = useControlPanelContext()
  
  return useMemo(() => ({
    betOptionsButtons: createButtonGroup('betOptions', context),
    chipsButtons: createButtonGroup('chips', context),
    controlsButtons: createButtonGroup('controls', context)
  }), [context])
}

// Button group factory
const createButtonGroup = (groupId: string, context: ControlPanelContext) => {
  const config = CONTROL_PANEL_CONFIG[groupId]
  return ButtonGroupFactory.create(config, context)
}
```

### New Button Group Factory
```typescript
// ButtonGroupFactories.ts
export class ButtonGroupFactory {
  static create(config: ButtonGroupConfig, context: ControlPanelContext): ControlPanelButton[] {
    switch (config.type) {
      case 'bet-actions':
        return this.createBetActionButtons(config, context)
      case 'selection':
        return this.createSelectionButtons(config, context)
      case 'mixed':
        return this.createMixedButtons(config, context)
      default:
        throw new Error(`Unknown button group type: ${config.type}`)
    }
  }
  
  private static createBetActionButtons(
    config: BetActionConfig, 
    context: ControlPanelContext
  ): ControlPanelButton[] {
    return config.buttons.map(buttonConfig => ({
      id: buttonConfig.id,
      src: `/assets/images/nav-buttons/${buttonConfig.icon}`,
      alt: this.generateAltText(buttonConfig),
      onClick: () => context.bettingActions[buttonConfig.action](),
      disabled: !config.enabledWhen(context.gameState)
    }))
  }
}
```

## Testing Strategy

### Unit Tests
```typescript
// ButtonGroupFactory.test.ts
describe('ButtonGroupFactory', () => {
  it('creates bet action buttons correctly', () => {
    const config = CONTROL_PANEL_CONFIG.betOptions
    const context = createMockContext({ Betting: true, autoplayActive: false })
    
    const buttons = ButtonGroupFactory.create(config, context)
    
    expect(buttons).toHaveLength(5)
    expect(buttons[0]).toMatchObject({
      id: 'repeat',
      disabled: false
    })
  })
  
  it('disables buttons when betting is not active', () => {
    const config = CONTROL_PANEL_CONFIG.betOptions
    const context = createMockContext({ Betting: false })
    
    const buttons = ButtonGroupFactory.create(config, context)
    
    buttons.forEach(button => {
      expect(button.disabled).toBe(true)
    })
  })
})

// useControlPanels.test.ts
describe('useControlPanels', () => {
  it('returns all button groups', () => {
    const { result } = renderHook(() => useControlPanels())
    
    expect(result.current).toHaveProperty('betOptionsButtons')
    expect(result.current).toHaveProperty('chipsButtons')
    expect(result.current).toHaveProperty('controlsButtons')
  })
  
  it('memoizes button groups correctly', () => {
    const { result, rerender } = renderHook(() => useControlPanels())
    const firstResult = result.current
    
    rerender()
    
    expect(result.current).toBe(firstResult)
  })
})
```

### Integration Tests
```typescript
// ControlPanel.integration.test.tsx
describe('Control Panel Integration', () => {
  it('handles bet action button clicks', async () => {
    const mockHandleBetAction = jest.fn()
    render(<ControlPanel buttons={betOptionsButtons} />)
    
    await user.click(screen.getByRole('button', { name: /repeat/i }))
    
    expect(mockHandleBetAction).toHaveBeenCalledWith('repeat')
  })
  
  it('updates button states based on game phase', () => {
    const { rerender } = render(<ControlPanel buttons={betOptionsButtons} />)
    
    // Test betting phase
    mockUseRoundPhase.mockReturnValue({ Betting: true })
    rerender(<ControlPanel buttons={betOptionsButtons} />)
    
    expect(screen.getByRole('button', { name: /repeat/i })).not.toBeDisabled()
    
    // Test non-betting phase
    mockUseRoundPhase.mockReturnValue({ Betting: false })
    rerender(<ControlPanel buttons={betOptionsButtons} />)
    
    expect(screen.getByRole('button', { name: /repeat/i })).toBeDisabled()
  })
})
```

### Performance Tests
```typescript
// Performance benchmarks
describe('Control Panel Performance', () => {
  it('renders button groups within performance budget', () => {
    const startTime = performance.now()
    
    render(<ControlPanel buttons={generateLargeButtonSet(100)} />)
    
    const endTime = performance.now()
    expect(endTime - startTime).toBeLessThan(16) // 60fps budget
  })
})
```

## Risk Mitigation

### Potential Issues
1. **Button State Synchronization**: Complex state dependencies
2. **Performance Regression**: Over-abstraction causing re-renders
3. **Configuration Complexity**: Making simple things complicated

### Mitigation Strategies
1. **Gradual Migration**: Implement one button group at a time
2. **Performance Monitoring**: Benchmark before/after changes
3. **Fallback System**: Keep original implementation as backup
4. **Configuration Validation**: Runtime validation of configurations

### Rollback Procedure
```typescript
// Feature flag for gradual rollout
const USE_NEW_CONTROL_PANELS = import.meta.env.VITE_USE_NEW_CONTROL_PANELS === 'true'

export const useControlPanels = () => {
  return USE_NEW_CONTROL_PANELS 
    ? useNewControlPanels() 
    : useLegacyControlPanels()
}
```

## Success Criteria

### Quantitative Metrics
- [ ] Mouse wheel scrolling works smoothly on desktop
- [ ] Drag-to-scroll functionality works on desktop and mobile
- [ ] Favorites button successfully opens popover
- [ ] Scroll indicators display correctly on all screen sizes
- [ ] useControlPanels hook reduced by 40% (from 218 to ~130 lines)
- [ ] No performance regression (<16ms render time)
- [ ] All existing functionality preserved

### Qualitative Metrics
- [ ] Improved user experience with dual scrolling
- [ ] Better mobile-first touch interactions
- [ ] Enhanced visual feedback with scroll indicators
- [ ] Cleaner hook structure with extracted configurations
- [ ] Maintained code readability and maintainability

### Acceptance Criteria
- [ ] All existing button functionality preserved
- [ ] Button state management works correctly
- [ ] Game phase transitions handled properly
- [ ] Special bet buttons function correctly
- [ ] Chip selection works as expected
- [ ] Favorites popover opens and closes correctly
- [ ] Scrolling works on all supported devices
- [ ] Visual indicators provide clear user feedback
- [ ] No accessibility regressions
- [ ] Mobile-first design principles maintained
