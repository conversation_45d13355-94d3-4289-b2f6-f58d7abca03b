import type { ControlPanelContext } from "./ControlPanelTypes"

/**
 * State management utilities for control panel buttons
 */

// Handle autoplay button state changes
export const handleAutoplayToggle = (context: ControlPanelContext) => {
  const { state, handlers } = context
  
  if (state.autoplayActive) {
    handlers.handleBetAction("stop-autoplay")
  } else {
    handlers.handleBetAction("start-autoplay")
  }
}

// Handle favorites button click
export const handleFavoritesClick = (context: ControlPanelContext) => {
  const { handlers } = context
  handlers.setFavoritesPopoverOpen(true)
}

// Handle statistics button click
export const handleStatsClick = (context: ControlPanelContext) => {
  const { handlers } = context
  handlers.setBetHistoryPopoverOpen(true)
}

// Handle hot/cold numbers toggle
export const handleHotColdToggle = (context: ControlPanelContext) => {
  const { state, handlers } = context
  handlers.setShowHotCold(!state.showHotCold)
}

// Handle past numbers toggle
export const handlePastNumbersToggle = (context: ControlPanelContext) => {
  const { state, handlers } = context
  handlers.setShowPastNumbers(!state.showPastNumbers)
}

// Handle game type toggle (specials)
export const handleGameTypeToggle = (context: ControlPanelContext) => {
  const { state, handlers } = context
  const newGameType = state.gameType === "special" ? "normal" : "special"
  handlers.setGameType(newGameType)
}

// Handle generic bet action
export const handleBetAction = (action: string, context: ControlPanelContext) => {
  const { handlers } = context
  handlers.handleBetAction(action)
}

// Check if button should be active
export const isButtonActive = (buttonId: string, context: ControlPanelContext): boolean => {
  const { state } = context
  
  switch (buttonId) {
    case 'autoplay':
      return state.autoplayActive
    case 'hot-cold':
      return state.showHotCold
    case 'past-numbers':
      return state.showPastNumbers
    case 'specials':
      return state.gameType === 'special'
    default:
      return false
  }
}

// Check if button should be disabled
export const isButtonDisabled = (buttonId: string, context: ControlPanelContext): boolean => {
  const { state } = context
  
  // Buttons that are always enabled
  const alwaysEnabledButtons = ['hot-cold', 'past-numbers', 'stats', 'specials']
  if (alwaysEnabledButtons.includes(buttonId)) {
    return false
  }
  
  // Most buttons require betting phase
  return !state.Betting
}
