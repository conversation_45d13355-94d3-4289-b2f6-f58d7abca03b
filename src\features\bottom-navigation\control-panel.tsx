import { useControlPanelLogic } from "./hooks/useControlPanelLogic"
import { ControlPanelRenderer } from "./components/control-panel-renderer"
import { themeStyles, type ControlPanelTheme } from "./components/theme-context"

export interface ControlPanelButton {
  id: string
  src: string
  alt: string
  onClick?: () => void
  disabled?: boolean
  active?: boolean
  value?: number | string
  isChip?: boolean
  aspect?: "square" | "freeform"
}

export interface ControlPanelProps {
  buttons: ControlPanelButton[]
  className?: string
  buttonSize?: "sm" | "md" | "lg"
  borderColor?: string
  backgroundColor?: string
  spacing?: number
  showTooltips?: boolean
  theme?: ControlPanelTheme
  autoResponsive?: boolean
  orientation?: "horizontal" | "vertical"
  progressiveLoading?: boolean
  ariaLabel?: string
  zeroGapForChips?: boolean
  // Popover state
  favoritesPopoverOpen?: boolean
  onFavoritesPopoverChange?: (open: boolean) => void
  betHistoryPopoverOpen?: boolean
  onBetHistoryPopoverChange?: (open: boolean) => void
}

/**
 * Control Panel - Acts as an orchestrator between logic and presentation
 *
 * This component coordinates between:
 * - useControlPanelLogic: Handles all business logic, state management, and side effects
 * - ControlPanelRenderer: Pure presentation component for rendering the UI
 *
 * The control panel no longer depends on theme context and uses default theme directly.
 */
export function ControlPanel({
  buttons,
  className,
  buttonSize,
  borderColor,
  backgroundColor,
  spacing = 14,
  showTooltips = false,
  theme,
  autoResponsive = true,
  orientation = "horizontal",
  progressiveLoading = true,
  ariaLabel = "Control panel",
  zeroGapForChips = false,
  favoritesPopoverOpen,
  onFavoritesPopoverChange,
  betHistoryPopoverOpen,
  onBetHistoryPopoverChange,
}: ControlPanelProps) {
  // Use the logic hook to handle all functionality
  const logic = useControlPanelLogic({
    buttons,
    buttonSize,
    spacing,
    autoResponsive,
    orientation,
    zeroGapForChips,
  })

  // Use default theme directly, no context dependency
  const defaultTheme = themeStyles.default
  const themeStyle = theme ? themeStyles[theme] : defaultTheme

  // Determine final styling values
  const finalBorderColor = borderColor || themeStyle.borderColor
  const finalBackgroundColor = backgroundColor || themeStyle.backgroundColor
  const finalActiveColor = themeStyle.activeColor
  const finalHoverColor = themeStyle.hoverColor

  // Render using the presentation component
  return (
    <ControlPanelRenderer
      buttons={buttons}
      className={className}
      borderColor={finalBorderColor}
      backgroundColor={finalBackgroundColor}
      activeColor={finalActiveColor}
      hoverColor={finalHoverColor}
      showTooltips={showTooltips}
      orientation={orientation}
      progressiveLoading={progressiveLoading}
      ariaLabel={ariaLabel}
      logic={logic}
      favoritesPopoverOpen={favoritesPopoverOpen}
      onFavoritesPopoverChange={onFavoritesPopoverChange}
      betHistoryPopoverOpen={betHistoryPopoverOpen}
      onBetHistoryPopoverChange={onBetHistoryPopoverChange}
    />
  )
}
