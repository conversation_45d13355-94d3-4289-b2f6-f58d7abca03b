import { ControlButton } from "./control-button"
import { FavoritesPopover } from "./favorites-popover"
import type { ControlPanelButton } from "../control-panel"

interface FavoritesButtonWithPopoverProps {
  button: ControlPanelButton
  open: boolean
  onOpenChange: (open: boolean) => void
  buttonSize: "sm" | "md" | "lg"
  showTooltip?: boolean
  activeColor: string
  hoverColor: string
}

export function FavoritesButtonWithPopover({
  button,
  open,
  onOpenChange,
  buttonSize,
  showTooltip,
  activeColor,
  hoverColor,
}: FavoritesButtonWithPopoverProps) {
  return (
    <div className="relative">
      <ControlButton
        id={button.id}
        src={button.src}
        alt={button.alt}
        onClick={() => onOpenChange(true)}
        disabled={button.disabled}
        active={button.active}
        size={buttonSize}
        showTooltip={showTooltip}
        activeColor={activeColor}
        hoverColor={hoverColor}
        value={button.value}
        isChip={button.isChip}
        aspect={button.aspect}
      />
      <FavoritesPopover open={open} onOpenChange={onOpenChange} />
    </div>
  )
}
