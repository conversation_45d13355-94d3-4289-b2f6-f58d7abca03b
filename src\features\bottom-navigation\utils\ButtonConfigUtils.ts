import type { ControlPanelButton } from "@/features/bottom-navigation/control-panel"
import type { 
  BetOptionConfig, 
  SpecialBetConfig, 
  SplitBetConfig,
  ControlPanelContext 
} from "../config/ControlPanelTypes"

/**
 * Utility functions for button configuration
 */

// Validate button configuration
export const validateButtonConfig = (config: BetOptionConfig | SpecialBetConfig | SplitBetConfig): boolean => {
  if (!config.id || !config.icon || !config.alt) {
    return false
  }
  
  if ('action' in config && !config.action) {
    return false
  }
  
  if ('cells' in config && (!config.cells || config.cells.length === 0)) {
    return false
  }
  
  return true
}

// Generate button ID from configuration
export const generateButtonId = (config: BetOptionConfig | SpecialBetConfig | SplitBetConfig): string => {
  return `btn-${config.id}`
}

// Get button icon path
export const getButtonIconPath = (iconName: string): string => {
  return `/assets/images/nav-buttons/${iconName}`
}

// Check if button should be disabled
export const shouldDisableButton = (
  config: BetOptionConfig | SpecialBetConfig | SplitBetConfig,
  context: ControlPanelContext
): boolean => {
  const { state } = context
  
  // Special cases for buttons that are always enabled
  if ('action' in config && ['hot-cold', 'past-numbers', 'stats'].includes(config.action)) {
    return false
  }
  
  // Most buttons require betting phase
  return !state.Betting
}

// Get button active state
export const getButtonActiveState = (
  config: BetOptionConfig | SpecialBetConfig | SplitBetConfig,
  context: ControlPanelContext
): boolean => {
  const { state } = context
  
  if ('action' in config) {
    switch (config.action) {
      case 'autoplay':
        return state.autoplayActive
      case 'hot-cold':
        return state.showHotCold
      case 'past-numbers':
        return state.showPastNumbers
      default:
        return false
    }
  }
  
  return false
}
