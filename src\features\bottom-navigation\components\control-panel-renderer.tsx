import { motion } from "motion/react"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import { ControlButton } from "./control-button"
import { ControlButtonSkeleton } from "./control-button-skeleton"
import ScrollFade from "@/components/ui/scroll-fade"
import type { ControlPanelButton } from "../control-panel"
import type { ControlPanelLogicState } from "../hooks/useControlPanelLogic"
import { FavoritesButtonWithPopover } from "./favorites-button-with-popover"
import { BetHistoryButtonWithPopover } from "./bet-history-button-with-popover"

export interface ControlPanelRendererProps {
  buttons: ControlPanelButton[]
  className?: string
  borderColor: string
  backgroundColor: string
  activeColor: string
  hoverColor: string
  showTooltips?: boolean
  orientation?: "horizontal" | "vertical"
  progressiveLoading?: boolean
  ariaLabel?: string
  logic: ControlPanelLogicState
  // Popover state
  favoritesPopoverOpen?: boolean
  onFavoritesPopoverChange?: (open: boolean) => void
  betHistoryPopoverOpen?: boolean
  onBetHistoryPopoverChange?: (open: boolean) => void
}

export function ControlPanelRenderer({
  buttons,
  className,
  borderColor,
  backgroundColor,
  activeColor,
  hoverColor,
  showTooltips = false,
  orientation = "horizontal",
  progressiveLoading = true,
  ariaLabel = "Control panel",
  logic,
  favoritesPopoverOpen = false,
  onFavoritesPopoverChange,
  betHistoryPopoverOpen = false,
  onBetHistoryPopoverChange,
}: ControlPanelRendererProps) {
  const {
    isVisible,
    loadedButtons,
    isDragging,
    forceShowButtons,
    buttonSize,
    finalSpacing,
    allImagesLoaded,
    animationConfig,
    isMobile,
    scrollAreaRef,
    checkScrollPosition,
    handleMouseDown,
    setRefs,
  } = logic

  return (
    <motion.div
      className={cn(
        "relative rounded-lg",
        orientation === "vertical" ? "flex flex-col" : "",
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
      transition={{
        opacity: {
          type: "spring",
          stiffness: animationConfig.stiffness,
          damping: animationConfig.damping,
        },
        y: {
          type: "spring",
          stiffness: animationConfig.stiffness,
          damping: animationConfig.damping,
        },
      }}
      style={{ backgroundColor: "transparent" }}
      ref={setRefs}
    >
      <div
        className={cn(
          "lg:pt-2 lg:px-2 lg:pb-1 rounded-lg relative flex items-center justify-center",
          orientation === "vertical" ? "flex-col" : "",
          orientation === "vertical"
            ? "control-panel-scroll-container vertical"
            : "control-panel-scroll-container"
        )}
        style={{
          border: `2px solid ${borderColor}`,
          backgroundColor: backgroundColor,
        }}
      >
        <ScrollFade
          direction={orientation === "vertical" ? "y" : "x"}
          size='sm'
        >
          <ScrollArea
            className={cn(
              "w-full",
              orientation === "vertical"
                ? "h-full overflow-y-auto control-panel-scroll-area vertical"
                : "overflow-x-auto control-panel-scroll-area",
              "flex items-center justify-center",
              isDragging ? "cursor-grabbing" : "cursor-grab"
            )}
            type='scroll'
            ref={scrollAreaRef}
            onScroll={checkScrollPosition}
            onMouseDown={handleMouseDown}
            scrollHideDelay={0}
          >
            <div
              className={cn(
                orientation === "vertical"
                  ? "flex flex-col items-center justify-center"
                  : "flex items-center justify-center",
                "min-w-max mx-auto"
              )}
              style={{
                gap: isMobile ? "2px" : `${finalSpacing}px`,
                paddingLeft: isMobile ? "2px" : "6px",
                paddingRight: isMobile ? "2px" : "6px",
              }}
              role='toolbar'
              aria-label={ariaLabel}
            >
              {buttons.map((button, index) => {
                if (!button || !button.id) return null

                const isLoaded =
                  forceShowButtons ||
                  (progressiveLoading
                    ? loadedButtons[button.id]
                    : allImagesLoaded)

                return (
                  <div
                    key={button.id}
                    className={cn(
                      "flex items-center justify-center",
                      orientation === "vertical" ? "flex-col" : "",

                      buttonSize === "sm"
                        ? "h-[56px]"
                        : buttonSize === "md"
                        ? "h-[72px]"
                        : "h-[88px]"
                    )}
                  >
                    {!isLoaded ? (
                      <ControlButtonSkeleton size={buttonSize} index={index} />
                    ) : button.id === "favourites" &&
                      onFavoritesPopoverChange ? (
                      <FavoritesButtonWithPopover
                        button={button}
                        open={favoritesPopoverOpen}
                        onOpenChange={onFavoritesPopoverChange}
                        buttonSize={buttonSize}
                        showTooltip={showTooltips}
                        activeColor={activeColor}
                        hoverColor={hoverColor}
                      />
                    ) : button.id === "stats" && onBetHistoryPopoverChange ? (
                      <BetHistoryButtonWithPopover
                        button={button}
                        open={betHistoryPopoverOpen}
                        onOpenChange={onBetHistoryPopoverChange}
                        buttonSize={buttonSize}
                        showTooltip={showTooltips}
                        activeColor={activeColor}
                        hoverColor={hoverColor}
                      />
                    ) : (
                      <ControlButton
                        id={button.id}
                        src={button.src}
                        alt={button.alt}
                        onClick={button.onClick}
                        disabled={button.disabled}
                        active={button.active}
                        size={buttonSize}
                        showTooltip={showTooltips}
                        activeColor={activeColor}
                        hoverColor={hoverColor}
                        value={button.value}
                        isChip={button.isChip}
                        aspect={button.aspect}
                      />
                    )}
                  </div>
                )
              })}
            </div>
          </ScrollArea>
        </ScrollFade>
      </div>
    </motion.div>
  )
}
