import type { ControlPanelButton } from "@/features/bottom-navigation/control-panel"
import type { SelectableCell } from "@/config/selector-config"
import type { BetActionType } from "@/stores/betting-store"
import type { GameType } from "@/stores/game-state-store"

// Configuration types for control panel buttons
export interface BetOptionConfig {
  id: string
  icon: string
  alt: string
  action: string
}

export interface SpecialBetConfig {
  id: string
  icon: string
  alt: string
  cells: SelectableCell[]
}

export interface SplitBetConfig {
  id: string
  icon: string
  alt: string
  action: string
}

// Button group types
export type ButtonGroupType = "bet-options" | "chips" | "controls"

// State management types
export interface ControlPanelState {
  Betting: boolean
  autoplayActive: boolean
  showHotCold: boolean
  showPastNumbers: boolean
  gameType: string
  selectedChip: any
}

// Handler types
export interface ControlPanelHandlers {
  handleBetAction: (actionType: BetActionType, params?: any) => void
  setFavoritesPopoverOpen: (open: boolean) => void
  setBetHistoryPopoverOpen: (open: boolean) => void
  setShowHotCold: (show: boolean) => void
  setShowPastNumbers: (show: boolean) => void
  setGameType: (gameType: GameType) => void
}

// Context type for button creation
export interface ControlPanelContext {
  state: ControlPanelState
  handlers: ControlPanelHandlers
}

// Button group configuration
export interface ButtonGroupConfig {
  type: ButtonGroupType
  buttons: (BetOptionConfig | SpecialBetConfig | SplitBetConfig)[]
}
