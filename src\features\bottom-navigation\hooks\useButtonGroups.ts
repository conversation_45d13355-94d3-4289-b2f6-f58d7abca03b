import { useMemo, useState } from "react"
import type { ControlPanelButton } from "@/features/bottom-navigation/control-panel"
import { rouletteChips } from "@/config/chip-config"
import { useBettingStore } from "@/stores/betting-store"
import { useGameStateStore } from "@/stores/game-state-store"
import { useControlPanelState } from "./useControlPanelState"
import { 
  BET_OPTIONS_CONFIG, 
  SPECIAL_BETS_CONFIG, 
  SPLIT_BETS_CONFIG 
} from "../config/ControlPanelConfig"
import {
  createBetOptionButton,
  createSpecialBetButton,
  createSplitBetButton,
  createSpecialsToggleButton,
} from "../config/ButtonGroupFactories"
import type { ControlPanelHandlers } from "../config/ControlPanelTypes"

/**
 * Hook to manage button groups for control panels
 * Provides organized button configurations
 */
export const useButtonGroups = () => {
  const state = useControlPanelState()
  const { handleBetAction, setFavoritesPopoverOpen } = useBettingStore()
  const { setGameType, setShowPastNumbers, setShowHotCold } = useGameStateStore()
  
  // State for bet history popover
  const [betHistoryPopoverOpen, setBetHistoryPopoverOpen] = useState(false)

  // Create handlers object
  const handlers: ControlPanelHandlers = {
    handleBetAction,
    setFavoritesPopoverOpen,
    setBetHistoryPopoverOpen,
    setShowHotCold,
    setShowPastNumbers,
    setGameType,
  }

  const context = { state, handlers }

  // Bet options panel buttons
  const betOptionsButtons = useMemo<ControlPanelButton[]>(
    () => BET_OPTIONS_CONFIG.map(config => createBetOptionButton(config, context)),
    [context]
  )

  // Chips panel buttons
  const chipsButtons = useMemo<ControlPanelButton[]>(
    () =>
      rouletteChips.map((chip) => ({
        id: `chip-${chip.value}`,
        src: chip.src,
        alt: `${chip.value} Credits`,
        onClick: () => {
          useBettingStore.getState().setSelectedChip(chip)
        },
        value: chip.value,
        isChip: true,
        active: state.selectedChip?.value === chip.value,
        disabled: !state.Betting,
      })),
    [state.selectedChip, state.Betting]
  )

  // Controls panel buttons
  const controlsButtons = useMemo<ControlPanelButton[]>(() => {
    const buttons: ControlPanelButton[] = []
    
    // Add specials toggle button
    buttons.push(createSpecialsToggleButton(context))
    
    // Add special bet buttons
    SPECIAL_BETS_CONFIG.forEach(config => {
      buttons.push(createSpecialBetButton(config, context))
    })
    
    // Add split bet buttons
    SPLIT_BETS_CONFIG.forEach(config => {
      buttons.push(createSplitBetButton(config, context))
    })
    
    return buttons
  }, [context])

  return {
    betOptionsButtons,
    chipsButtons,
    controlsButtons,
    betHistoryPopoverOpen,
    setBetHistoryPopoverOpen,
  }
}
